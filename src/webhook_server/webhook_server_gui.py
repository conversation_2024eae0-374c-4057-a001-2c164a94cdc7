"""Webhook服务器GUI主界面模块。

此模块提供了Webhook服务器的图形用户界面，包括：
- 服务器启动/停止控制
- 实时数据显示
- 配置管理
- 系统监控
"""

import argparse
import configparser
import copy
import functools
import logging
import multiprocessing
import re
import subprocess
import sys
import threading
import time
import tkinter
from datetime import datetime
from pathlib import Path
from tkinter import messagebox
from typing import Dict, List, Optional, Union, Any
from zoneinfo import ZoneInfo

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from common.constants import common_constants
from webhook_server.utils import webhook_server_utils, webhook_gui_utils
from webhook_server.config import config_check, constants, gui_constants
from webhook_server.models import gui_server_info, server_data, server_data_manager
from common.models import gui_widgets, single_instance_meta
from webhook_server.utils import config_lock
from common.utils import self_log, gui_utils, ttkb_gui_utils, process_monitor
from common.utils.logging_utils import logger_print
from common.utils.network_utils import get_local_lan_ip
from common.utils.config_utils import section_to_dict, get_miss_key_in_dict
from common.utils.time_utils import check_runtime_allowed, format_time_monotonic_interval
from common.utils.process_utils import process_end_output

logger: Optional[logging.Logger] = None


class WebhookServerGUI(metaclass=single_instance_meta.SingleInstanceMeta):
    """Webhook服务器GUI主界面类。

    提供Webhook服务器的图形用户界面，包括服务器控制、数据显示等功能。
    传入的config_path为服务端配置文件路径，其必须有效。

    Attributes:
        NO_DATA_SHOW_TAG: 在没有数据时实时数据表格中行的标签，该标签会设置颜色
    """
    # 在没有数据时,实时数据表格中行的标签,该标签会设置颜色
    NO_DATA_SHOW_TAG = 'no_data_show'

    def __init__(self, server_config_path: str) -> None:
        """初始化Webhook服务器GUI。

        Args:
            server_config_path: 服务端配置文件路径
        """
        logger_print(msg="initializing webhook server gui", custom_logger=logger)
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING, zone=common_constants.DEFAULT_TIMEZONE)
        # 服务端配置文件路径
        logger_print(msg="setting up server config path", custom_logger=logger)
        self.validate_config_file_path(server_config_path)
        self.config_file_path = server_config_path
        self.config_file_time_zone:Optional[ZoneInfo] = None
        logger_print(msg=f"using provided config path: {server_config_path}", custom_logger=logger)

        # 服务端状态信息
        # 服务端总运行时间
        self.total_runtime_seconds = 0
        # 服务端点击启动按钮的时间 而非第一次初始化启动的时间 time.monotonic()
        self.server_start_time:Optional[float] = None        
        self.status_label: Optional[ttkb.Label] = None
        # 显示第一次启动的时间
        self.server_initial_startup_time:Optional[ttkb.Label] = None

        self.cpu_meter: Optional[gui_widgets.PrecisionMeter] = None
        self.start_btn: Optional[ttkb.Button] = None
        self.stop_btn: Optional[ttkb.Button] = None
        self.message_data_table: Optional[gui_widgets.TreeviewWithFixedGrid] = None
        # 保存完整message内容的映射，用于双击复制完整内容
        self._full_message_map: Dict[str, str] = {}
        # gui界面中按钮手动停止后台更新服务端状态信息线程
        self._stop_server_event = threading.Event()
        self.gui_server_info:Optional[gui_server_info.GUIServerInfo] = None
        self._server_refresh_thread: Optional[threading.Thread] = None
        self.had_initialized_startup=False

        # 性能优化：减少不必要的UI更新
        self._last_ui_update = 0
        self._ui_update_interval = 1.0  # 1秒更新一次UI

        # 由于该值没有真正使用到，是使用在保存到配置文件和配置文件加载到该变量上，所以所有的变量的值都需要是字符串类型
        self.server_config_values:dict[str,Any] = {}
        # 该变量和client_tree一起使用
        self.client_info_entries = {}
        self.file_client_info_entries={}
        self.log_config_path:Optional[str] = None
        self.error_occured = False
        self.message_data_manager: Optional[server_data_manager.WebhookDataManager] = None
        self.no_data_show=True
        self.notification_bar_text:str = gui_constants.DEFAULT_NOTIFICATION_MESSAGE

        # gui元素变量
        self.server_config_dialog: Optional[ttkb.Toplevel] = None
        self.client_info_dialog: Optional[ttkb.Toplevel] = None
        self.about_dialog: Optional[ttkb.Toplevel] = None
        self.client_info_table: Optional[gui_widgets.TreeviewWithFixedGrid] = None # 用户自定义客户端标识信息的gui表格
        self.client_right_click_menu: Optional[ttkb.Menu] = None
        self.config_input_widgets: Dict[str, Union[tkinter.Entry,dict[str,tkinter.Entry]]] = {}  # 用户自定义服务端配置项的Entry组件映射,可能一个项对应多个Entry组件
        # gui字体
        self.ui_font_family:Optional[str]=None
        self.server_total_uptime_var:Optional[ttkb.StringVar]=None
        self.server_pid_var:Optional[ttkb.StringVar]=None
        self.server_memory_var:Optional[ttkb.StringVar]=None
        # 服务端进程
        self.server_process:Optional[subprocess.Popen]=None
        self._stop_server_process_event:Optional[multiprocessing.Event]=None

        # 服务端进程监控器，专门监控server_process
        self.server_monitor:Optional[process_monitor.ProcessMonitor]=None


        # 主界面 设置主窗口大小固定不可调整
        self.root = ttkb.Window(themename=gui_constants.DEFAULT_THEME,title=gui_constants.SOFTWARE_NAME,size=gui_constants.WEBHOOK_SERVER_GUI_SIZE,resizable=(False, False),alpha=0.98,iconphoto=gui_constants.SMALL_ICON_PATH)
        self.after_main_win_with_except()

    def show_initial_table_tooltip(self):
        """
        显示初始表格提示信息,必须在主界面出现之后执行该方法
        """
        time.sleep(0.5)
        self._no_data_show(show_msg="服务端未启动不显示数据")

    def after_main_win_with_except(self):
        """
        在创建主窗口之后,后续需要在初始化过程中需要做的事情:
        其必须在try/except中调用块中调用,一旦出现异常直接就可以关闭主窗口
        """
        try:
            ttkb_gui_utils.set_available_unified_font_families(self.root)
            self.ui_font_family= ttkb_gui_utils.first_available_font_family
            # 控件的初始化必须在root创建之后
            self.server_total_uptime_var = ttkb.StringVar(value="服务端运行总时长:0天 00:00:00")
            self.server_pid_var=ttkb.StringVar(value=gui_constants.SERVER_PID_DEFAULT_VAR)
            self.server_memory_var=ttkb.StringVar(value=gui_constants.SERVER_MEMORY_DEFAULT_VAR)

            self.server_process: Optional[multiprocessing.Process] = None
            self._stop_server_process_event=multiprocessing.Event()

            # 读取配置文件并加载到当前实例属性中
            self.load_config()
            logger_print(msg="configuration loaded successfully", custom_logger=logger)

            # 创建界面
            self.create_widgets()
            logger_print(msg="gui widgets created successfully", custom_logger=logger)
            gui_utils.main_gui_after_create_widgets_comm_do(self.root)

            # 监听关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            logger_print(msg="webhook server gui initialization completed", custom_logger=logger)

            threading.Thread(target=self.show_initial_table_tooltip, name="initial_table_tooltip_thread", daemon=True).start()
        except BaseException:
            logger_print(msg=f"初始化 webhook server gui出现异常", custom_logger=logger, use_exception=True)
            if hasattr(self, 'server_monitor') and self.server_monitor is not None:
                self.server_monitor.stop()
            if hasattr(self,'config_manager') and self.config_manager is not None:
                self.config_manager.unregister_config()
            if hasattr(self, 'root') and self.root is not None:
                self.root.destroy()
                self.root=None
            raise

    def validate_config_file_path(self,server_config_path:str):
        try:
            self.config_manager.main_gui_load_config(server_config_path)
        except Exception as e:
            webhook_server_utils.write_error_to_temp_file(str(e))
            sys.exit(1)

    def create_widgets(self):
        ttkb_gui_utils.show_notification(win=self.root, text=self.notification_bar_text, font_family_size=(self.ui_font_family, 12))
        # 创建顶部菜单栏
        menu_bar = ttkb.Menu(self.root)
        self.root.config(menu=menu_bar)

        # 创建"设置"菜单
        settings_menu = ttkb.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="服务端配置", command=self.open_config_dialog)
        settings_menu.add_command(label="发信设备标识信息", command=self.show_client_info_dialog)
        settings_menu.add_command(label="关于", command=self.show_about_dialog)

        # 创建"问题修复"菜单
        fix_menu = ttkb.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="问题修复", menu=fix_menu)
        fix_menu.add_command(label="网络通信修复", command=self.fix_network_communication)

        # 主内容区域（使用Frame作为容器）
        content_frame = ttkb.Frame(self.root)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=(5,0))

        # 数据表格区域（固定高度适合显示10行数据）
        data_frame = ttkb.LabelFrame(content_frame, text="实时最新10条数据")
        # 计算适合10行数据的高度：表头(30px) + 10行×25px + padding(40px)
        table_height = 30 + (10 * 25) + 40  # 320px
        data_frame.configure(height=table_height)
        data_frame.pack(fill=X, padx=10, pady=(0, 10))
        data_frame.pack_propagate(False)  # 防止子组件改变父容器大小

        # 创建表格 gui_constants.RED_COLOR
        column_dicts=[
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'message', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '数据内容[双击-复制完整数据内容]', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 290},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'reception_time', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '接收时间', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 175},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'client_key', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '设备标识', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 140},
            {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'is_read', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '是否被其他设备读取', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 160},
        ]
        self.message_data_table = gui_widgets.TreeviewWithFixedGrid(parent=data_frame, font_family=self.ui_font_family, font_size=10, columns= column_dicts, need_tree=False, selectmode="browse")
        self.message_data_table.tag_configure(WebhookServerGUI.NO_DATA_SHOW_TAG, foreground=gui_utils.rgb_to_hex((95, 4, 4)))
        # 表格填充容器
        self.message_data_table.pack(fill=BOTH, expand=True, padx=10, pady=1)

        # 绑定双击事件用于复制功能:实时数据表格中行的双击事件
        gui_utils.bind_double_click_handler(self.message_data_table, self.on_real_time_data_table_double_click)

        # 显示服务端描述信息
        self.show_server_describe(content_frame)

        # 按钮区域（固定在底部）side=ttkb.BOTTOM,  .pack(fill=X, expand=False)
        btn_frame = ttkb.Frame(self.root)
        btn_frame.pack(fill=X, expand=False, padx=20, pady=(0,5))
        self.start_btn = ttkb.Button(btn_frame, text="启动服务端", command=self.start_server)
        self.start_btn.pack(side=LEFT, padx=10)
        self.stop_btn = ttkb.Button(btn_frame, text="停止服务端", command=self.stop_server, state=DISABLED)
        self.stop_btn.pack(side=LEFT, padx=10)

    def _build_notice_hover_frame(self,hover_label_frame:ttkb.Frame):
        """
        创建服务端注意事项提示信息区域
        :param hover_label_frame:  是TooltipLabel中popup的frame
        """
        # 设置悬浮框背景色和边框
        hover_label_frame.configure(relief="solid", borderwidth=1)

        # 标题
        title_label = ttkb.Label(hover_label_frame, text="📋 程序使用注意事项",font=(self.ui_font_family, 11, "bold"),bootstyle="info") # noqa
        title_label.pack(anchor=W, padx=8, pady=(8, 5))

        # 分隔线
        separator = ttkb.Separator(hover_label_frame, orient="horizontal")
        separator.pack(fill=X, padx=8, pady=2)

        # 注意事项内容
        notices = [
            "1. 服务端配置界面配置运行时段:",
            "   • 必须符合时间格式(HH:MM:SS)且在00:00-23:59范围内",
            "   • 开始时间和结束时间一致表示服务端支持全天运行",
            "   • 开始时间小于结束时间表示常规情况下服务端从开始时间运行到结束时间",
            "   • 开始时间大于结束时间表示支持跨天运行,服务端从今天的开始时间运行到明天的结束时间,如05:00-04:00就表示从今天的5点运行到明天的4点",
            "",
            "2. 服务端运行期间:",
            "   • 不能修改设置中的配置项",
            "   • 双击实时数据表格中的指定行可复制完整数据内容",
            "",
            "3. 发信设备的网络必须和本程序运行的机器的网络处于同一局域网",
            "",
            "4. 发信设备请求链接信息:",
            f"   • Header: X-Client-Key=发信设备标识信息界面中设备标识值",
            f"   • URL: http://{get_local_lan_ip()}:{self.server_config_values['port']}/webhook/save", # noqa
            "",
            "5. 发信内容要求:",
            "   • 字符尽量不要使用特殊字符,可能无法正常显示",
        ]
        # 正则表达式匹配行标题
        title_pattern = re.compile(r"^\d+\.\s")
        for notice in notices:
            if notice == "":  # 空行
                ttkb.Label(hover_label_frame, text="").pack(anchor=W, padx=8, pady=1)
            elif notice.startswith("   •"):  # 子项目
                ttkb.Label(hover_label_frame, text=notice,
                         font=(self.ui_font_family, 9),
                         foreground="#666666").pack(anchor=W, padx=8, pady=1)
            elif title_pattern.match(notice):  # 主项目
                ttkb.Label(hover_label_frame, text=notice,
                         font=(self.ui_font_family, 10, "bold"),
                         bootstyle="warning").pack(anchor=W, padx=8, pady=(3, 1)) # noqa
            else:  # 其他文本
                ttkb.Label(hover_label_frame, text=notice,
                         font=(self.ui_font_family, 9),
                         foreground="#333333").pack(anchor=W, padx=8, pady=1)

        # 底部间距
        ttkb.Label(hover_label_frame, text="").pack(pady=5)

    def show_server_describe(self,content_frame:ttkb.Frame):
        """显示服务端描述信息
        在real_time_data_table下面显示服务端状态信息，如果有必要，也可以补充其他信息：
            1. self.status_label 服务端允许状态
            2. 在程序打开之后启动服务端运行总时长 【实时刷新】
            3. 系统监控信息：进程PID、内存使用、CPU使用率 【实时刷新】
            4. 用户使用注意事项：
            4.1 在服务端运行期间不能修改设置中的配置项
            4.2 发信设备网络必须和运行当前程序的机器的网络在同一个局域网中
        """
        server_running_frame=ttkb.Frame(content_frame)
        server_running_frame.pack(fill=X, expand=False)
        # 服务端状态标签:其由于会涉及到颜色的设置,不方便使用StringVar;
        server_status_font = (self.ui_font_family,12)
        server_running_font = (self.ui_font_family,10)
        
        # 服务端运行时文字监控信息
        status_running_text_info_frame = ttkb.Frame(server_running_frame)
        status_running_text_info_frame.pack(side=LEFT, fill=X, expand=True, anchor=N)
        # 创建标签 - 移除width参数，使用固定像素宽度
        self.status_label = ttkb.Label(status_running_text_info_frame,text="服务端未运行",font=server_status_font)
        monitor_pid_label = ttkb.Label(status_running_text_info_frame,textvariable=self.server_pid_var,font=server_running_font)
        monitor_memory_label = ttkb.Label(status_running_text_info_frame,textvariable=self.server_memory_var,font=server_running_font)
        self.server_initial_startup_time=ttkb.Label(status_running_text_info_frame,text=gui_constants.SERVER_INITIAL_STARTUP_TIME_DEFAULT_VAR,font=server_status_font)
        runtime_label = ttkb.Label(status_running_text_info_frame,textvariable=self.server_total_uptime_var,font=server_status_font)


        rows = [
            [self.status_label, monitor_pid_label, monitor_memory_label],
            [self.server_initial_startup_time, runtime_label]
        ]
        for r, row in enumerate(rows):
            for c, widget in enumerate(row):
                if widget:
                    widget.grid(row=r, column=c, sticky=W, padx=2, pady=0)
        for i in range(2):  # 2行
            status_running_text_info_frame.grid_rowconfigure(i, weight=0)
        # 使用固定像素宽度，避免字体变化导致布局问题
        status_running_text_info_frame.grid_columnconfigure(0, weight=0, minsize=315)  # 状态/启动时间列，固定315px
        status_running_text_info_frame.grid_columnconfigure(1, weight=0, minsize=170)  # PID/运行时长列，固定170px
        status_running_text_info_frame.grid_columnconfigure(2, weight=0, minsize=90)  # 内存列，固定90px

        # 服务端运行时视图监控信息
        server_running_view_info_frame = ttkb.Frame(server_running_frame)
        server_running_view_info_frame.pack(side=LEFT, fill=X, expand=True, anchor=N)
        self.cpu_meter = ttkb_gui_utils.create_cpu_meter(server_running_view_info_frame, 130, 8)
        self.cpu_meter.pack(side=TOP,anchor=NW,padx=(0,0),pady=5)

        # 注意事项区域
        notice_frame = ttkb.Frame(content_frame)
        notice_frame.pack(pady=(0, 10), fill=X)
        notice_frame.grid_columnconfigure(0, weight=1)  # 左半边，空着
        notice_frame.grid_columnconfigure(1, weight=0)  # 右边，放label
        label_container = ttkb.Frame(notice_frame)
        label_container.grid(row=0, column=1, sticky="e")
        hover_label = gui_widgets.TooltipLabel(label_container, self._build_notice_hover_frame, position='right', text='▲程序使用注意事项▼', offset_x=-720, offset_y=-320, bootstyle="inverse-info")
        hover_label.grid(row=0, column=1, sticky="ew", padx=(0, 10))


    def _no_data_show(self,show_msg:str):
        """显示空数据提示信息"""
        self.message_data_table.clear_all_data()
        # 清除完整message映射
        self._full_message_map.clear()
        # 如果没有数据，在表格中插入居中提示信息
        logger.info("no data available, showing centered empty message spanning all columns")
        self.message_data_table.insert("", "end", values=('',show_msg,),tags=(WebhookServerGUI.NO_DATA_SHOW_TAG,))
        self.no_data_show=True

    def refresh_data(self,new_data:List[server_data.MessageToGUI]):
        """刷新表格数据"""
        logger.info("refreshing data in real-time data table")
        try:
            # 清除现有数据
            self.message_data_table.delete(*self.message_data_table.get_children())
            # 清除完整message映射
            self._full_message_map.clear()
            # 获取新数据
            logger.debug("fetching recent data from data manager")
            logger.debug(f"fetched {len(new_data)} data items")
            if not new_data:
                self._no_data_show(show_msg="当前没有数据发送到本服务端")
            else:
                self.no_data_show=False
                # 有数据时插入实际数据
                logger.debug("data available, inserting data into tree")
                # 插入新数据
                new_message_data=[]
                full_messages = []  # 保存完整message内容
                for i, item in enumerate(new_data):
                    # 是否被其他设备读取
                    auth_status = "未被读取" if item.is_read == 0 else "已被其他设备读取数据"
                    # 处理message长度，超过 SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH 个字符则截断为 SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH 个字符+...
                    display_message = item.message
                    if len(display_message) > gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH:
                        display_message = display_message[:gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH] + "..."
                    logger.debug(f"inserting item {i+1}: message_length={len(item.message)}, client_key={item.client_key}, auth_status={auth_status}")
                    new_message_data.append({'message': display_message,'reception_time': item.reception_time, 'client_key': item.client_key, 'is_read': auth_status})
                    full_messages.append(item.message)  # 保存完整内容

                # 定义行处理器来保存完整message内容
                def message_row_processor(treeview, item_id, row_data, index, tag):  # noqa
                    # 保存完整message内容到映射中
                    self._full_message_map[item_id] = full_messages[index]

                self.message_data_table.add_rows(new_message_data, message_row_processor)
            logger.info("data refresh completed successfully")
        except Exception:  # noqa
            logger.exception("error during data refresh")
            raise

    def on_real_time_data_table_double_click(self):
        """处理实时数据表格双击事件，实现数据内容复制功能"""
        if self.no_data_show:
            self.show_no_copied_popup()
            return
        logger.debug("webhook real-time data table double click event triggered")
        # 获取双击位置
        selected=self.message_data_table.selection()
        if not selected or len(selected) > 1 or not self.message_data_table.exists(selected[0]):
            return
        try:
            item_id = selected[0]
            # 选择该行
            self.message_data_table.selection_set(item_id)
            # 获取完整的message内容
            message_content = self._full_message_map[item_id]

            # 复制到剪贴板
            if message_content:
                self.copy_to_clipboard(message_content)
                logger.debug(f"copied message content to clipboard via double click, content length: {len(message_content)}")
                # 显示复制成功提示窗口
                self.show_copy_success_popup(message_content)
        except Exception:  # noqa
            logger.exception("error during webhook real-time data table double click handling")

    def copy_to_clipboard(self, text):
        """将文本复制到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.root.update()  # 确保剪贴板更新
            logger.debug("webhook data message copied to clipboard successfully!")
        except Exception:  # noqa
            logger.exception("error during copying webhook data message to clipboard")

    def show_copy_success_popup(self, message_content):
        """显示复制成功的提示窗口，3秒后自动消失"""
        try:
            logger.debug("showing webhook data message copy success popup")

            # 显示复制的内容（截取前xx个字符用于预览）
            show_char_length = gui_constants.SERVER_GUI_MESSAGE_SHOW_CHAR_LENGTH
            content_preview = message_content[:show_char_length] + "..." if len(message_content) > show_char_length else message_content

            # 创建提示窗口，完全不设置大小限制
            popup = ttkb.Toplevel(master=self.root,title="系统剪贴板已复制完整内容成功",takefocus=True,topmost = True,transient=self.root)
            ttkb_gui_utils.comm_child_win_do(popup, self.root)

            # 创建提示内容，使用最简单的布局
            ttkb.Label(popup, text="数据内容完整复制成功！", font=(self.ui_font_family, 12, "bold")).pack(padx=20, pady=(20, 10))
            ttkb.Label(popup, text=f"数据前{show_char_length}个字符预览：{content_preview}", font=(self.ui_font_family, 9)).pack(padx=20, pady=(0, 20))

            # 3秒后自动关闭窗口
            popup.after(1200, popup.destroy) # noqa
            logger.debug("copy success popup displayed, will auto-close in 1 second")
        except Exception:  # noqa
            logger.exception("error during showing copy success popup")

    def show_no_copied_popup(self):
        """显示无法复制的提示窗口弹窗，3秒后自动消失"""
        try:
            # 显示提示消息
            content='当前服务端未运行,无数据可复制'

            # 创建提示窗口，完全不设置大小限制
            popup = ttkb.Toplevel(master=self.root,title="未运行不可复制",takefocus=True,topmost = True,transient=self.root)
            ttkb_gui_utils.comm_child_win_do(popup, self.root)

            # 创建提示内容，使用最简单的布局
            ttkb.Label(popup, text=content, font=(self.ui_font_family, 12, "bold")).pack(padx=20, pady=(20, 10))

            # 3秒后自动关闭窗口
            popup.after(3000, popup.destroy) # noqa
        except Exception:  # noqa
            logger.exception("error during showing no copied popup")

    def open_config_dialog(self):
        if self.server_config_dialog and self.server_config_dialog.winfo_exists():
            self.server_config_dialog.lift()
            return

        self.server_config_dialog = ttkb.Toplevel(master=self.root,title="服务端配置",size=gui_constants.SERVER_CONFIG_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.root)
        ttkb_gui_utils.comm_child_win_do(self.server_config_dialog, self.root)

        server_config_frame = ttkb.Frame(self.server_config_dialog)
        server_config_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 使用 grid 布局统一控制整体布局
        config_frame = ttkb.LabelFrame(server_config_frame, text="服务端配置")
        config_frame.grid(row=0, column=0, sticky=NSEW, padx=10, pady=(0, 10))

        self.config_input_widgets.clear()

        for row, config_key in enumerate(gui_constants.USER_CUSTOM_KEYS):
            ttkb.Label(config_frame, text=f"{constants.SERVER_KEY_DESC[config_key]}:").grid(row=row, column=0, sticky=E, padx=5, pady=5)

            if config_key == "run_time":
                # run_time配置项使用特殊的布局
                self._set_run_time_entry(config_frame,row)
            else:
                # 其他配置项使用普通Entry 输入框
                server_custom_config_entry = ttkb.Entry(config_frame, width=25)
                server_custom_config_entry.grid(row=row, column=1, sticky=W, padx=5, pady=5)

                # 如果配置中已有值则显示
                existing_value = self.server_config_values.get(config_key)
                if existing_value:
                    server_custom_config_entry.insert(0, existing_value)

                # 添加对应的配置项的校验器
                if config_key == "api_key":
                    ttkb_gui_utils.comm_entry_validate(server_custom_config_entry, validate_func=constants.SERVER_API_KEY_PATTERN.fullmatch)

                self.config_input_widgets[config_key] = server_custom_config_entry

        # 保存按钮
        save_frame = ttkb.Frame(server_config_frame)
        save_frame.grid(row=1, column=0, sticky=E, pady=(10, 0))

        ttkb.Button(save_frame, text="保存服务端配置", command=self.save_config_changes).pack(anchor=E, padx=10)

        # 关闭事件
        self.server_config_dialog.protocol("WM_DELETE_WINDOW", self.server_config_dialog.destroy)

    def _set_run_time_entry(self,config_frame:ttkb.LabelFrame,cur_row:int):
        """设置run_time的Entry控件"""
        cur_run_time='run_time'
        # 为run_time创建特殊的Spinbox控件布局
        spinbox_width = 2
        time_frame = ttkb.Frame(config_frame)
        time_frame.grid(row=cur_row, column=1, sticky=W, padx=5, pady=5)

        # 开始时间标签
        ttkb.Label(time_frame, text="开始时间:", font=(self.ui_font_family, 9)).grid(row=0, column=0, sticky=W, padx=(0, 5))

        # 开始时间 - 小时Spinbox
        start_hour_spinbox = ttkb.Spinbox(time_frame, from_=0, to=23, width=spinbox_width, format="%02.0f")
        start_hour_spinbox.grid(row=0, column=1, padx=2)

        ttkb.Label(time_frame, text=":", font=(self.ui_font_family, 10, "bold")).grid(row=0, column=2)

        # 开始时间 - 分钟Spinbox
        start_minute_spinbox = ttkb.Spinbox(time_frame, from_=0, to=59, width=spinbox_width, format="%02.0f")
        start_minute_spinbox.grid(row=0, column=3, padx=2)

        # 分隔符
        ttkb.Label(time_frame, text=" - ", font=(self.ui_font_family, 10, "bold")).grid(row=0, column=4, padx=5)

        # 结束时间标签
        ttkb.Label(time_frame, text="结束时间:", font=(self.ui_font_family, 9)).grid(row=0, column=5, sticky=W, padx=(0, 5))

        # 结束时间 - 小时Spinbox
        end_hour_spinbox = ttkb.Spinbox(time_frame, from_=0, to=23, width=spinbox_width, format="%02.0f")
        end_hour_spinbox.grid(row=0, column=6, padx=2)

        ttkb.Label(time_frame, text=":", font=(self.ui_font_family, 10, "bold")).grid(row=0, column=7)

        # 结束时间 - 分钟Spinbox
        end_minute_spinbox = ttkb.Spinbox(time_frame, from_=0, to=59, width=spinbox_width, format="%02.0f")
        end_minute_spinbox.grid(row=0, column=8, padx=2)

        # 解析现有配置值并设置到Spinbox中,不存在则使用默认值,其格式在使用前就进行校验,是一定可以解析的
        existing_value = self.server_config_values.get(cur_run_time)
        if existing_value:
            start_time_str, end_time_str = existing_value.split('-')
            start_hour, start_minute = start_time_str.split(':')
            end_hour, end_minute = end_time_str.split(':')
            start_hour_spinbox.set(start_hour)
            start_minute_spinbox.set(start_minute)
            end_hour_spinbox.set(end_hour)
            end_minute_spinbox.set(end_minute)
        # 格式校验提醒
        ttkb_gui_utils.comm_entry_validate(start_hour_spinbox, validate_func=constants.HOUR_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(start_minute_spinbox, validate_func=constants.MINUTE_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(end_hour_spinbox, validate_func=constants.HOUR_PATTERN.fullmatch)
        ttkb_gui_utils.comm_entry_validate(end_minute_spinbox, validate_func=constants.MINUTE_PATTERN.fullmatch)

        # 存储Spinbox控件的引用，用于后续获取值
        self.config_input_widgets[cur_run_time] = {
            'start_hour': start_hour_spinbox,
            'start_minute': start_minute_spinbox,
            'end_hour': end_hour_spinbox,
            'end_minute': end_minute_spinbox
        }


    def show_about_dialog(self):
        if self.about_dialog and self.about_dialog.winfo_exists():
            self.about_dialog.lift()
            return
        cur_gui_width = 550
        # 如果一个内容存在很长,这个就是其自动换行的行长度
        mult_content_line_additional_width = 150
        self.about_dialog = ttkb.Toplevel(master=self.root,title="关于",size=(cur_gui_width, 250),takefocus=True,topmost = True,resizable=(False, False),transient=self.root)
        ttkb_gui_utils.comm_child_win_do(self.about_dialog, self.root)
        # 主框架
        main_frame = ttkb.Frame(self.about_dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 标题
        title_label = ttkb.Label(main_frame,text=gui_constants.SOFTWARE_NAME,font=(self.ui_font_family, 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 使用网格布局创建整齐的键值对
        labels = [
            ("版本", gui_constants.VERSION),
            ("联系方式", gui_constants.CONTACT_INFORMATION),
            ("发布日期", gui_constants.RELEASE_TIME)
        ]

        # 创建标签和内容
        for row, (label_text, value_text) in enumerate(labels, start=1):
            # 标签列 (左对齐)
            ttkb.Label(main_frame, text=label_text + "：",
                      justify=RIGHT, anchor=E).grid(
                row=row, column=0, sticky=E, padx=(0, 5), pady=2)

            # 值列 (左对齐)
            ttkb.Label(main_frame, text=value_text,anchor=W).grid(row=row, column=1, sticky=W, pady=2)

        # 简介 (单独一行)
        row = len(labels) + 1
        ttkb.Label(main_frame, text="简要介绍：",anchor=E).grid(row=row, column=0, sticky=E, padx=(0, 5), pady=(10, 0))

        # 多行简介 (自动换行)
        ttkb.Label(main_frame,text=gui_constants.INSTRUCTION_SOFTWARE_USE,wraplength=cur_gui_width-mult_content_line_additional_width,justify=LEFT).grid(row=row, column=1, sticky=W, pady=(10, 0))

        # 许可协议 (单独一行)
        row += 1
        ttkb.Label(main_frame, text="许可协议：",anchor=E).grid(row=row, column=0, sticky=E, padx=(0, 5), pady=(10, 0))
        # 许可协议内容 自动换行
        license_agreement_content = gui_constants.LICENSE_AGREEMENT.replace(" ", "\u00A0")
        ttkb.Label(main_frame,text=license_agreement_content,wraplength=cur_gui_width-mult_content_line_additional_width,justify=LEFT).grid(row=row, column=1, sticky=W, pady=(10, 0))

        # 配置列权重
        main_frame.columnconfigure(0, weight=1, minsize=80)  # 标签列最小宽度
        main_frame.columnconfigure(1, weight=3)  # 内容列占3/4宽度

        # 绑定关闭事件
        self.about_dialog.protocol("WM_DELETE_WINDOW", self.about_dialog.destroy)

    def show_client_info_dialog(self):
        # 如果对话框已存在，则提到最前
        if getattr(self, "client_info_dialog", None) and self.client_info_dialog.winfo_exists():
            self.client_info_dialog.lift()
            return

        self.client_info_dialog = ttkb.Toplevel(master=self.root,title="发信设备标识管理",size=gui_constants.CLIENT_INFO_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.root)
        ttkb_gui_utils.comm_child_win_do(self.client_info_dialog, self.root)
        client_info_frame = ttkb.Frame(self.client_info_dialog)
        client_info_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 发信设备标识信息表格
        client_info_table_frame = ttkb.LabelFrame(client_info_frame, text="发信设备标识信息", bootstyle="card", padding=10) # noqa
        client_info_table_frame.pack(fill=X, pady=(0,10))
        client_info_table_frame.configure(height=200)
        client_info_table_frame.grid_propagate(False)
        client_info_table_frame.rowconfigure(0, weight=1)
        client_info_table_frame.columnconfigure(0, weight=1)
        column_dicts=[
                    {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'key',gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '发信设备标识', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 200},
                     {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'description',gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '发信设备描述信息', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 320},
                      ]
        self.client_info_table = gui_widgets.TreeviewWithFixedGrid(parent=client_info_table_frame, font_family=self.ui_font_family, font_size=11, columns= column_dicts, need_tree=False, selectmode="extended", bootstyle="info")

        scrollbar = ttkb.Scrollbar(client_info_table_frame ,orient="vertical", command=self.client_info_table.yview,bootstyle="round") # noqa
        self.client_info_table.configure(yscrollcommand=scrollbar.set)
        # 放置表格和滚动条
        self.client_info_table.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky=NS)

        # 添加双击事件绑定
        gui_utils.bind_double_click_handler(self.client_info_table, self.__edit_client)

        # 新增右键菜单功能
        self.client_right_click_menu = ttkb.Menu(self.client_info_table, tearoff=0)
        self.client_right_click_menu.add_command(label="编辑", command=self.__edit_client)
        # 绑定右键点击事件
        gui_utils.right_click_show_menu(self.client_info_table, self.client_right_click_menu)
        cur_client_info_data=[]
        if self.client_info_entries:
            for client_key, client_desc in self.client_info_entries.items():
                cur_client_info_data.append({'key':client_key,'description':client_desc})
            self.client_info_table.add_rows(cur_client_info_data)

        # 操作按钮区域
        btn_frame = ttkb.Frame(client_info_frame)
        btn_frame.pack(fill=X, pady=10)

        ttkb.Button(btn_frame, text="添加",bootstyle="secondary",  command=self.__add_client).pack(side=LEFT) # noqa
        ttkb.Button(btn_frame, text="编辑",bootstyle="secondary",  command=self.__edit_client).pack(side=LEFT, padx=5) # noqa
        ttkb.Button(btn_frame, text="删除",bootstyle="secondary",  command=self.__delete_client).pack(side=LEFT) # noqa


        ttkb.Button(btn_frame, text="保存修改",bootstyle="primary", command=self.save_client_config).pack(side=RIGHT) # noqa
        def on_close():
            # 检查是否有未保存的修改
            if self.file_client_info_entries != self.client_info_entries:
                if messagebox.askyesno("未保存的修改","有未保存的修改，是否放弃更改?",parent=self.client_info_dialog):
                    # 放弃更改并关闭窗口
                    self.client_info_entries=copy.deepcopy(self.file_client_info_entries)
                    gui_utils.gui_close(self.client_info_dialog)
                else:
                    # 用户选择继续编辑，不关闭窗口
                    return
            else:
                gui_utils.gui_close(self.client_info_dialog)

        # 绑定关闭事件
        self.client_info_dialog.protocol("WM_DELETE_WINDOW", on_close)


    # 保存或者修改:[当cur_client_id值存在时,即修改] --- client_key不能重复
    def __save_client_info(self, dialog: ttkb.Toplevel, key_entry: ttkb.Entry, desc_entry: ttkb.Entry, cur_selected_id: str = None):
        # 默认选择节点id存在即client_tree中存在该节点
        client_key = key_entry.get()
        client_desc = desc_entry.get()
        if not client_key or not client_desc:
            messagebox.showerror("错误", "发信设备标识和描述不能为空",parent=dialog)
            return
        if not webhook_server_utils.check_one_client_info(client_key, client_desc):
            messagebox.showerror("错误", constants.ERROR_CLIENT_INPUT_MSG,parent=dialog)
            return
        # 新的设备标识不能与已有的重复
        old_client_key = None
        if cur_selected_id:
            old_client_key = self.client_info_table.item(cur_selected_id, 'values')[0]
        if client_key in self.client_info_entries and (cur_selected_id is None or old_client_key != client_key):
            messagebox.showerror("错误", "发信设备标识不能和已有的设备标识重复,请重新输入重试",parent=dialog)
            return
        if cur_selected_id:
            # 修改发信设备信息
            self.client_info_table.item(cur_selected_id, values=(client_key, client_desc))
            del self.client_info_entries[old_client_key]
        else:
            # 添加发信设备信息
            new_client_info_data=[{'key':client_key,'description':client_desc}]
            self.client_info_table.add_rows(new_client_info_data)
        self.client_info_entries[client_key] = client_desc
        dialog.destroy()

    def __show_add_client_dialog(self, dialog_title: str, cur_selected_id: str = None):
        # 显示添加发信设备标识对话框
        cur_key = ""
        cur_desc = ""
        if cur_selected_id:
            cur_key, cur_desc = self.client_info_table.item(cur_selected_id, 'values')
        dialog = ttkb.Toplevel(master=self.client_info_dialog,title=dialog_title,size=gui_constants.CLIENT_NEW_DIALOG_SIZE,takefocus=True,topmost = True,resizable=(False, False),transient=self.client_info_dialog)
        ttkb_gui_utils.comm_child_win_do(dialog, self.client_info_dialog)
        dialog.columnconfigure(1, weight=1)
        ttkb.Label(dialog, text="发信设备标识:").grid(row=0, column=0, padx=10, pady=5, sticky=E)
        key_entry = ttkb.Entry(dialog, width=40)
        key_entry.insert(0, cur_key)
        key_entry.grid(row=0, column=1, padx=10, pady=5, sticky=EW)
        ttkb_gui_utils.comm_entry_validate(key_entry, validate_func=constants.CLIENT_KEY_PATTERN.fullmatch)

        ttkb.Label(dialog, text="发信设备描述:").grid(row=1, column=0, padx=10, pady=5, sticky=E)
        desc_entry = ttkb.Entry(dialog, width=40)
        desc_entry.insert(0, cur_desc)
        desc_entry.grid(row=1, column=1, padx=10, pady=5, sticky=EW)
        def check_desc_input(input_str: str)->bool:
            # 描述输入框必须大于1个字符
            return input_str and len(input_str)>1

        ttkb_gui_utils.comm_entry_validate(desc_entry, validate_func=check_desc_input)

        btn_frame = ttkb.Frame(dialog)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=15)

        save_funct=functools.partial(self.__save_client_info, dialog=dialog, key_entry=key_entry, desc_entry=desc_entry, cur_selected_id=cur_selected_id)
        ttkb.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=RIGHT)
        ttkb.Button(btn_frame, text="保存",command=save_funct).pack(side=RIGHT, padx=(0, 10))

        dialog.bind("<Return>", lambda e: self.__save_client_info(dialog, key_entry, desc_entry, cur_selected_id))
        dialog.bind("<Escape>", lambda e: dialog.destroy())
        # 绑定关闭事件
        dialog.protocol("WM_DELETE_WINDOW", dialog.destroy)

    def __add_client(self):
        # 添加发信设备标识对话框
        self.__show_add_client_dialog("添加发信设备信息")

    def __edit_client(self):
        selected = self.client_info_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个发信设备信息进行编辑",parent=self.client_info_dialog)
            return
        # 只能单选
        if len(selected) > 1:
            messagebox.showwarning("警告", "只能选择一个发信设备信息进行编辑",parent=self.client_info_dialog)
            return
        self.__show_add_client_dialog("编辑发信设备信息", selected[0])

    def __delete_client(self):
        # 选中节点id[可多选]
        selected = self.client_info_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请至少先选择一个发信设备信息进行删除",parent=self.client_info_dialog)
            return
        if selected:
            for item_id in selected:
                del self.client_info_entries[self.client_info_table.item(item_id, 'values')[0]]
                self.client_info_table.delete(item_id)
        self.client_info_table.update_separators()

    def _show_operation_error(self, error, error_msg, parent_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]], no_exit=True):
        """集中处理用户操作错误"""
        self.error_occured = True
        gui_utils.handle_code_error(error=error, error_msg=error_msg, parent_gui=parent_gui, no_exit=no_exit)


    def load_config(self):
        try:
            server_config = configparser.ConfigParser(interpolation=None)
            # key保持原本的大小写
            server_config.optionxform = str
            server_config.read(self.config_file_path, encoding="utf-8")
            # 加载服务端配置:其中存在用户界面自定义配置项，这时对应的配置项就不需要检验
            self.server_config_values = section_to_dict(server_config, "server", True)
            # 加载发信设备标识信息
            if "client_info" in server_config:
                self.client_info_entries = section_to_dict(server_config, "client_info", allow_empty_section=True)
                self.file_client_info_entries=copy.deepcopy(self.client_info_entries)
        except Exception as server_config_ex:
            self._show_operation_error(error=server_config_ex, error_msg="加载服务端配置失败",parent_gui=self.root,no_exit=False)

        # 用户界面自定义配置项缺失，提示用户必须填写所有必填字段
        miss_user_custom_keys = get_miss_key_in_dict(dict_obj=self.server_config_values, required_keys=gui_constants.USER_CUSTOM_KEYS)
        if not self.client_info_entries:
            miss_user_custom_keys.add("client_info")
        if miss_user_custom_keys:
            key_desc_set = {constants.SERVER_KEY_DESC[user_custom_key] for user_custom_key in miss_user_custom_keys}
            msg=f"服务端配置项缺失: 【{', '.join(key_desc_set)}】, 请填写之后保存配置然后运行服务端!"
            self.notification_bar_text=msg+"   "+self.notification_bar_text

        self.log_config_path = self.server_config_values["log_config_path"]
        self.config_file_time_zone=ZoneInfo(self.server_config_values["time_zone"])
        global logger
        # gui和服务端日志文件统一
        self_log.setup_logging(self.log_config_path, time_zone=self.config_file_time_zone, filename_prefix=self.server_config_values["app_name"])
        logger = logging.getLogger(__name__)

    def check_all_config_before_start(self):
        # 在服务端正式启动前,进行配置项的校验 --- 不需要进行保存
        if self._server_process_alive():
            self._show_operation_error(error=None,error_msg= "服务端正在运行,请不要重复启动服务端!",parent_gui=self.root)
            return
        self.error_occured=False
        # 用户自定义配置项不能为空
        try:
            config_check.check_server_config_file_to_runtime_webhook(self.config_file_path)
        except Exception as check_config_file_except:
            self._show_operation_error(error=check_config_file_except,error_msg= str(check_config_file_except),parent_gui=self.root)
        # 阻断后续执行
        if self.error_occured:
            return

    def _check_runtime_allowed(self)->bool:
        """检查当前时间是否在允许的运行时间段内"""
        try:
            run_time = self.server_config_values.get("run_time", "07:00-07:00")
            can_run, always_run, start_time, end_time, now = check_runtime_allowed(run_time, self.config_file_time_zone)
            
            if not can_run:
                # 计算下次可运行时间
                next_run_info = WebhookServerGUI._calculate_next_run_time(start_time, end_time, now)
                error_msg = f"⏰ 服务端运行时间限制\n\n当前时间: {now.strftime('%H:%M')}\n运行时间段: {run_time}\n\n{next_run_info}\n\n💡 提示: 如需修改运行时间段，请前往 [设置] → [服务端配置] 修改"
                
                # 显示错误对话框，并询问是否要打开配置
                result = messagebox.askyesnocancel(
                    "服务端运行时间限制", 
                    error_msg + "\n\n是否现在打开服务端配置进行修改？",
                    parent=self.root
                )
                
                if result:  # 用户选择"是"
                    self.open_config_dialog()
                
                logger.warning(f"server start blocked: current time {now} not in allowed runtime {run_time}")
                return False
            
            return True
            
        except Exception as e:
            logger.exception("error checking runtime allowance")
            self._show_operation_error(error=e, error_msg=f"检查运行时间段时出错: {e}", parent_gui=self.root)
            return False
    @staticmethod
    def _calculate_next_run_time(start_time, end_time, now):
        """计算下次可运行时间的提示信息"""
        # 如果是跨天运行,那么在当前不能运行的情况下,其肯定是今天的开始时间
        # 如果当前时间在今天的结束时间之后且当前是跨天的,说明已经在运行时段内了
        # 跨天或当前时间早于开始时间 => 今天的开始时间
        if end_time < start_time or now < start_time:
            day = "今天"
        else:
            # 正常情况且当前时间已过结束时间 => 明天的开始时间
            day = "明天"
        return f"下次可运行时间: {day} {start_time.strftime('%H:%M')}"

    def save_config_changes(self):
        if self._server_process_alive():
            self._show_operation_error(error=None,error_msg= "服务端正在运行,请先停止服务端,再保存服务端配置项!",parent_gui=self.server_config_dialog)
            return
        cur_server_config_values = {}
        for config_key, entry_widget in self.config_input_widgets.items():
            if config_key == "run_time":
                # 处理run_time的Spinbox控件
                start_hour = entry_widget['start_hour'].get()
                start_minute = entry_widget['start_minute'].get()
                end_hour = entry_widget['end_hour'].get()
                end_minute = entry_widget['end_minute'].get()
                # 格式化为HH:MM-HH:MM格式
                run_time_value = f"{start_hour}:{start_minute}-{end_hour}:{end_minute}"
                cur_server_config_values[config_key] = run_time_value
            else:
                # 其他配置项使用普通Entry的get方法
                cur_server_config_values[config_key] = str(entry_widget.get())
        try:
            self.config_manager.update_config('server', cur_server_config_values)
            self.server_config_values.update(cur_server_config_values)
        except Exception as check_server_config_except:
            self._show_operation_error(error=check_server_config_except,error_msg= f"保存服务端配置失败\n {check_server_config_except}",parent_gui=self.server_config_dialog)
            return
        if messagebox.askyesno("成功","服务端配置项保存成功,是否退出服务端配置界面?",parent=self.server_config_dialog):
            gui_utils.gui_close(self.server_config_dialog)

    def save_client_config(self):
        if self._server_process_alive():
            self._show_operation_error(error=None, error_msg= "服务端正在运行,请先停止服务端,再保存发信设备标识信息!",parent_gui=self.client_info_dialog)
            return
        try:
           self.config_manager.update_config("client_info", self.client_info_entries)
           self.file_client_info_entries=copy.deepcopy(self.client_info_entries)
        except Exception as check_client_config_except:
           self._show_operation_error(error=check_client_config_except,error_msg= str(check_client_config_except),parent_gui=self.client_info_dialog)
           return
        if messagebox.askyesno("成功","发信设备标识信息保存成功，是否退出发信设备标识信息界面?",parent=self.client_info_dialog):
            gui_utils.gui_close(self.client_info_dialog)
    def start_server(self):
        logger.info("starting server process")

        self.error_occured=False
        self.check_all_config_before_start()  # 确保保存最新配置
        if self.error_occured:
            logger.error("configuration check failed, aborting server start")
            return

        # 检查运行时间段
        if not self._check_runtime_allowed():
            return

        try:
            logger.info("creating server process with config path: %s", self.config_file_path)

            # 启动服务端进程
            self._stop_server_process_event.clear()

            self.server_process = webhook_server_utils.start_independent_process(config_path=self.config_file_path, argument_param='--config', relative_script_path='./src/webhook_server/models/child_webhook_server.py')
            logger.info("server process started with pid: %s", self.server_process.pid)

            if self.server_process.poll() is not None:
                raise ValueError("启动服务端进程失败!")

            logger.info("server process is alive, updating ui state")
            self._update_start_btn_state(False)
            self.status_label.config(text="服务端运行中",foreground="green")
            self._no_data_show(show_msg="服务端正在启动,请稍候...")
            
            # 启动服务端进程监控器
            logger.info(f"starting server process monitor for pid: {self.server_process.pid}")
            self._start_server_process_monitor()
            
            logger.info("initializing data manager")
            self.message_data_manager = server_data_manager.WebhookDataManager(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,table_name=self.server_config_values["message_data_table_name"], zone=self.config_file_time_zone, enable_sql_logging=bool(self.server_config_values["enable_sql_logging"]))
            logger.info("scheduling server status check in 1 second")
            self.start_server_refresh_thread()
            logger.info("server start process completed successfully")
        except Exception as start_server_exp:
            logger.exception("server start process failed!", exc_info=True)
            self._show_operation_error(error=start_server_exp, error_msg="启动服务端失败",parent_gui=self.root)
            # 清理资源
            self._stop_server_process_monitor()
            self.server_process = None
            self._update_start_btn_state(True)
            self.status_label.config(text="服务端启动失败",foreground="red")

    def start_server_refresh_thread(self):
        """启动服务端时启动后台刷新数据+前端刷新ui"""
        if hasattr(self, '_server_refresh_thread') and self._server_refresh_thread is not None and self._server_refresh_thread.is_alive():
            return
        self._stop_server_event.clear()
        # 等待服务端启动完成
        self.wait_started()
        self._server_refresh_thread = threading.Thread(target=self.update_server_status,daemon=True,name="gui_webhook_server_refresh_thread")
        self._server_refresh_thread.start()
        self.root.after(1000, self.refresh_ui)  # type: ignore

    def stop_server_refresh_thread(self):
        """停止服务端时停止后台刷新数据和前端刷新ui"""
        self._stop_server_event.set()
        self.stop_server_ui()
        self._stop_server_refresh_thread()

    def _stop_server_refresh_thread(self):
        if hasattr(self, '_server_refresh_thread') and self._server_refresh_thread is not None and self._server_refresh_thread.is_alive():
            try:
                logger.info("waiting for server refresh thread to finish")
                self._server_refresh_thread.join(timeout=5.0)
                if self._server_refresh_thread.is_alive():
                    logger.warning("server refresh thread did not finish within timeout")
                else:
                    logger.info("server refresh thread finished successfully")
            except Exception as thread_error:
                logger_print(msg=f"error joining server refresh thread: {thread_error}", custom_logger=logger, use_exception=True, exception=thread_error)
            finally:
                self._server_refresh_thread = None

    def wait_started(self):
        """等待服务端启动完成
        args:
            is_thread: 是否在后台线程中执行[update_server_status],默认False,表示在主线程中执行
        """
        # 服务端刚刚启动,其状态信息可能还未初始化,因此需要等待一段时间
        while not self._stop_server_event.is_set():
            if self._server_process_alive():
                # server_start_time用来计算服务端运行时长
                self.server_start_time=time.monotonic() - self.total_runtime_seconds
                break
            self._stop_server_event.wait(1)

    def update_server_status(self):
        """在后台线程中更新服务端状态信息"""
        logger.debug("update server status")
        while not self._stop_server_event.is_set():
            server_process = getattr(self, 'server_process', None)

            # 最新服务端状态信息
            cur_gui_server_info=gui_server_info.GUIServerInfo()
            need_break=False
            try:
                if not server_process:
                    cur_gui_server_info.server_status_message="服务端未启动"
                    cur_gui_server_info.server_status_color="black"
                    self.server_process = None
                    need_break=True
                elif server_process.poll() is not None:
                    exit_code= server_process.poll()
                    cur_gui_server_info.exit_code=exit_code
                    logger.warning(f"server process (pid: {server_process.pid}) exited with code: {exit_code}")
                    if exit_code !=0:
                        error_msg:str =f"服务端进程异常退出,退出码: ({exit_code})\n请自行查询日志解决问题或者联系开发者"
                        cur_gui_server_info.exit_reason=error_msg
                        cur_gui_server_info.server_status_message="服务端异常停止"
                        cur_gui_server_info.server_status_color="red"
                    else:
                        logger.info("server process exited normally")
                        cur_gui_server_info.server_status_message="服务端已停止"
                        cur_gui_server_info.server_status_color="black"
                    logger.info(f"server process exited with code: {exit_code}")
                    process_end_output(server_process)
                    cur_gui_server_info.server_button_flag=True
                    # 服务端进程退出时停止监控器
                    self._stop_server_process_monitor()
                    self.server_process = None
                    need_break=True
                else:
                    logger.debug("server process is still alive, scheduling next status check in 5 seconds")
                    cur_gui_server_info.server_status_message="服务端运行中"
                    cur_gui_server_info.server_status_color="green"
                    cur_gui_server_info.server_button_flag=False
                    cur_gui_server_info.new_data=self.message_data_manager.get_recent_data(10)
                    cur_gui_server_info.cur_get_new_data=True
                    self._stop_server_event.wait(timeout=0.8)
            except BaseException: # noqa
                logger.exception("error during server status check!", exc_info=True)
                # 异常时停止监控器
                self._stop_server_process_monitor()
                self.server_process = None
                cur_gui_server_info.stop_server_flag=True
                cur_gui_server_info.server_button_flag=True
                cur_gui_server_info.server_status_message="服务端异常停止"
                cur_gui_server_info.server_status_color="red"
                self._stop_server_event.set()
                self.status_label.config(text="服务端异常停止",foreground="red")
            self.gui_server_info=cur_gui_server_info
            if need_break:
                self._stop_server_event.set()
                break

    def refresh_ui(self):
        """根据后台线程更新的服务端状态信息,刷新ui界面,在服务端启动之后执行"""
        # 在这里不进行进程的检测[_server_process_alive],由于其已经在 update_server_status 中进行了检测,如果这里继续进行检测,可能会出现这里的ui先停止更新,而后台线程还在更新没有将最后一次的状态信息更新到gui_server_info,导致ui显示不正确
        if not hasattr(self, 'root') or not gui_utils.gui_exist(self.root) or not hasattr(self, '_stop_server_event') or self._stop_server_event.is_set():
            self.stop_server_refresh_thread()
            return

        # 等待后台刷新数据然后更新ui
        current_gui_server_info=getattr(self, 'gui_server_info',None)
        # 等待后台刷新数据完成
        if not current_gui_server_info:
            self.root.after(500, self.refresh_ui) # type: ignore
            return
        if not self.had_initialized_startup:
            self.server_initial_startup_time.config(text=f"服务端初始启动时间: {datetime.now(self.config_file_time_zone).strftime(common_constants.DATETIME_FORMAT)}")
            self.had_initialized_startup=True

        self._update_start_btn_state(active=current_gui_server_info.server_button_flag)
        self.status_label.config(text=current_gui_server_info.server_status_message,foreground=current_gui_server_info.server_status_color)
        self.total_runtime_seconds,server_running_duration= format_time_monotonic_interval(start_timestamp=self.server_start_time)
        self.server_total_uptime_var.set(f"服务端运行总时长:{server_running_duration}")

        # 更新系统监控信息 - 使用服务端进程监控器 server_monitor是在启动服务端时启动的,因此server_monitor属性是一定存在的
        pid, cpu_usage, memory_usage = self.server_monitor.get_usage()
        self.server_pid_var.set(f"服务端PID: {pid}")
        self.server_memory_var.set(f"内存使用: {memory_usage / 1024 /1024:.2f} MB")
        ttkb_gui_utils.update_cpu_meter(self.cpu_meter, cpu_usage, gui_constants.CPU_THRESHOLD_SEGMENT_STYLE)

        if current_gui_server_info.cur_get_new_data:
            self.refresh_data(current_gui_server_info.get_data())

        # 动态调整刷新频率
        current_time = time.time()
        if current_time - self._last_ui_update >= self._ui_update_interval:
            self._last_ui_update = current_time
            next_update_delay = 1000  # 正常刷新间隔
        else:
            next_update_delay = 500   # 快速刷新间隔

        self.root.after(next_update_delay, self.refresh_ui) # type: ignore

    def _reset_system_monitor_info(self):
        """重置系统监控信息显示"""
        try:
            logger_print(msg="resetting system monitor info", custom_logger=logger, log_level=logging.DEBUG)
            if hasattr(self, 'server_pid_var'):
                self.server_pid_var.set(gui_constants.SERVER_PID_DEFAULT_VAR)
            if hasattr(self, 'server_memory_var'):
                self.server_memory_var.set(gui_constants.SERVER_MEMORY_DEFAULT_VAR)
            if hasattr(self, 'cpu_meter'):
                ttkb_gui_utils.update_cpu_meter(self.cpu_meter, 0.0, gui_constants.CPU_THRESHOLD_SEGMENT_STYLE)
            logger_print(msg="system monitor info reset completed", custom_logger=logger, log_level=logging.DEBUG)
        except Exception as e:
            self._show_operation_error(error=e, error_msg="重置系统监控信息失败",parent_gui=self.root)

    def stop_server_ui(self):
        """在服务端停止时ui样式"""
        self._stop_server_event.set()
        if hasattr(self, 'gui_server_info') and self.gui_server_info is not None:
            cur_gui_server_info = self.gui_server_info
            self.status_label.config(text=cur_gui_server_info.server_status_message,foreground=cur_gui_server_info.server_status_color)
            exit_code=getattr(cur_gui_server_info, 'exit_code', None)
            if exit_code is not None and exit_code!=0:
                self._show_operation_error(error=None, error_msg=cur_gui_server_info.exit_reason,parent_gui=self.root)
            if cur_gui_server_info.stop_server_flag:
                cur_gui_server_info.stop_server_flag=False
                self._stop_server_refresh_thread()
                self.stop_server_process()
        else:
            self.status_label.config(text="服务端已停止",foreground="black")
        self.gui_server_info=None
        self.stop_no_data_show()
        self._reset_system_monitor_info()
        self._update_start_btn_state(True)

    def _update_start_btn_state(self,active:bool) -> None:
        """
        更新启动/停止按钮状态
        Args: active: 是否激活启动按钮 (True=启动可用, False=停止可用)
        """
        if hasattr(self,'start_btn') and hasattr(self,'stop_btn'):
            self.start_btn.config(state=NORMAL if active else DISABLED)
            self.stop_btn.config(state=DISABLED if active else NORMAL)

    def stop_server_process(self):
        """停止服务端子进程"""
        if not hasattr(self, 'root') or not gui_utils.gui_exist(self.root) or not self._server_process_alive():
            logger_print(msg="no running server process found", custom_logger=logger)
            return
        logger_print(msg="stopping server process", custom_logger=logger)
        logger_print(msg=f"terminating server process (pid: {self.server_process.pid})", custom_logger=logger)
        
        # 停止服务端进程监控器
        self._stop_server_process_monitor()
        
        # 终止服务端进程
        self._stop_server_process_event.set()
        self.server_process.terminate()
        self.server_process.wait(timeout=5.0)
        if self._server_process_alive():
            logger_print(msg="force killing server process", custom_logger=logger, log_level=logging.WARNING)
            self.server_process.kill()
        self.status_label.config(text="服务端已停止",foreground="black")
        self.server_process = None
    def stop_server(self):
        self.stop_server_process()
        self.stop_server_refresh_thread()
    def stop_no_data_show(self):
        """服务端停止时不显示数据"""
        # 清除数据显示
        logger_print(msg="clearing data display after server stop", custom_logger=logger)
        # 清除现有数据（包括空数据提示）
        if hasattr(self, 'message_data_table'):
            self.message_data_table.delete(*self.message_data_table.get_children())
            self._no_data_show(show_msg="服务端停止不显示数据")

    def on_closing(self):
        # 考虑到配置在加载时出现异常和配置时出现异常的情况,这个时候保存配置就会使得原本在配置文件中的配置丢失,所以这里不保存配置
        # 关闭前检查服务端是否运行
        if self._server_process_alive():
            if messagebox.askokcancel("退出", "服务端正在运行，确定要退出吗？",parent=self.root):
                self.stop_server()
            else:
                # 取消退出
                return
        
        # 确保停止所有监控器
        self._stop_server_process_monitor()
        gui_utils.gui_close(self.root)
        self.root=None

    def _server_process_alive(self)->bool:
        """判断服务端进程是否存活"""
        return hasattr(self, "server_process") and self.server_process is not None and self.server_process.poll() is None

    def _start_server_process_monitor(self):
        """启动服务端进程监控器"""
        try:
            # 停止之前的服务端监控器（如果存在）
            self._stop_server_process_monitor()
            
            if self.server_process and self.server_process.poll() is None:
                server_pid = self.server_process.pid
                logger.info(f"creating server process monitor for pid: {server_pid}")
                
                # 创建专门监控服务端进程的监控器实例
                self.server_monitor = process_monitor.ProcessMonitor(interval=3, pid=server_pid)
                self.server_monitor.start()
                
                logger.info(f"server process monitor started successfully for pid: {server_pid}")
            else:
                logger.warning("cannot start server process monitor: server process not running")
        except Exception as e:
            logger.error(f"failed to start server process monitor: {e}")
            self.server_monitor = None

    def _stop_server_process_monitor(self):
        """停止服务端进程监控器"""
        try:
            if hasattr(self, 'server_monitor') and self.server_monitor is not None:
                logger.info("stopping server process monitor")
                self.server_monitor.stop()
                self.server_monitor = None
                logger.info("server process monitor stopped successfully")
        except Exception as e:
            logger.error(f"failed to stop server process monitor: {e}")
            self.server_monitor = None

    def fix_network_communication(self):
        """网络通信修复功能"""
        logger_print(msg="network communication fix requested", custom_logger=logger)

        # 检测当前服务端是否启动
        if self._server_process_alive():
            logger_print(msg="server is running, cannot perform network fix", custom_logger=logger)
            messagebox.showwarning(
                title="警告",
                message="不能在服务端启动时进行网络修复，请先停止服务端后再尝试。",
                parent=self.root
            )
            return

        logger_print(msg="server is not running, proceeding with network fix", custom_logger=logger)
        # 服务端未启动，调用网络修复函数
        try:
            webhook_gui_utils.restore_network_connection(self.root)
            logger_print(msg="network communication fix completed successfully", custom_logger=logger)
        except Exception as e:
            logger_print(msg="network communication fix failed", custom_logger=logger, use_exception=True, exception=e)
            messagebox.showerror(
                title="错误",
                message=f"网络通信修复失败：{str(e)}",
                parent=self.root
            )
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    config_path = parser.parse_args().config
    main_app = WebhookServerGUI(config_path)
    main_app.root.mainloop()
