"""可视化操作工具类模块。

此模块提供了GUI界面相关的工具函数，包括：
- 窗口居中和定位
- 错误处理
- 事件绑定
- 图像处理
- 颜色转换
"""

import logging
import sys
import tkinter
from tkinter import messagebox, ttk
from typing import Optional, Union

from PIL import Image, ImageDraw, ImageFilter, ImageTk

from common.constants import gui_const
# server_utils已拆分，相关函数已迁移到对应的新utils模块

logger = logging.getLogger(__name__)


def center_window_on_screen(main_gui: tkinter.Tk) -> None:
    """将主屏幕设置在屏幕中心，一般需要居中偏上显示。

    Args:
        main_gui: 主窗口对象
    """
    from common.utils.logging_utils import logger_print
    logger_print(msg="centering main window", custom_logger=logger)
    main_gui.update_idletasks()

    width = main_gui.winfo_width()
    height = main_gui.winfo_height()

    if width <= 1 or height <= 1:
        width = gui_const.DEFAULT_GUI_WIDTH
        height = gui_const.DEFAULT_GUI_HEIGHT
        main_gui.geometry(f"{width}x{height}")
        main_gui.update_idletasks()

    screen_width = main_gui.winfo_screenwidth()
    screen_height = main_gui.winfo_screenheight()

    x = (screen_width - width) // 2
    # 稍微偏上一点
    y = (screen_height - int(height*1.4)) // 2

    main_gui.geometry(f"+{x}+{y}")
    main_gui.update_idletasks()
    logger_print(
        msg=f"main window centered at ({x}, {y})", custom_logger=logger
    )


def center_dialog_on_parent(
    child_gui: tkinter.Toplevel,
    parent_gui: Union[tkinter.Tk, tkinter.Toplevel]
) -> None:
    """将子窗口设置在父窗口的中心。

    Args:
        child_gui: 子窗口对象
        parent_gui: 父窗口对象
    """
    logger_print(msg="centering child window", custom_logger=logger)
    child_gui.update_idletasks()
    parent_gui.update_idletasks()

    width = child_gui.winfo_width()
    height = child_gui.winfo_height()

    if width <= 1 or height <= 1:
        width = gui_const.DEFAULT_GUI_WIDTH
        height = gui_const.DEFAULT_GUI_HEIGHT
        child_gui.geometry(f"{width}x{height}")
        child_gui.update_idletasks()

    # 获取父窗口的位置和大小
    parent_x = parent_gui.winfo_x()
    parent_y = parent_gui.winfo_y()
    parent_width = parent_gui.winfo_width()
    parent_height = parent_gui.winfo_height()

    x = parent_x + (parent_width - width) // 2
    y = parent_y + (parent_height - height) // 2

    child_gui.geometry(f"+{x}+{y}")
    logger_print(msg=f"child window centered at ({x}, {y}) relative to parent", custom_logger=logger)

# 在可视化程序中如果存在程序问题,直接弹出错误信息框,然后关闭程序
def handle_code_error(error:BaseException|None, error_msg:str,parent_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]],no_exit:bool=True):
    """集中处理代码错误:直接停止程序"""
    from common.utils.logging_utils import logger_print
    if error:
        logger_print(msg=f"{error_msg}: {error}", custom_logger=logger, use_exception=True, exception=error)
    else:
        logger_print(msg=error_msg, custom_logger=logger, log_level=logging.ERROR)
    need_exit = not no_exit
    if need_exit:
        error_msg=f"{error_msg}\n\n请联系管理员处理"
    messagebox.showerror("程序错误",error_msg,parent=parent_gui)
    if need_exit:
      sys.exit(1)

# 右键显示菜单
def right_click_show_menu(cur_tree:ttk.Treeview, cur_menu:tkinter.Menu):
    def right_click_show_menu_handler(event: tkinter.Event):
        """处理Treeview右键点击事件，显示上下文菜单"""
        # 获取点击位置的行
        row_id = cur_tree.identify_row(event.y)
        if row_id:
            # 选中右键点击的行
            cur_tree.selection_set(row_id)
            # 在鼠标位置显示菜单
            cur_menu.post(event.x_root, event.y_root)
    cur_tree.bind("<Button-3>", func=right_click_show_menu_handler)

# 双击事件处理函数
def bind_double_click_handler(cur_tree:ttk.Treeview,funct:callable):
    def double_click_handler(event: tkinter.Event):
        """处理Treeview双击事件，触发指定函数操作"""
        region = cur_tree.identify("region", event.x, event.y)
        # 只响应在单元格上的双击（排除表头等其他区域）
        if region == "cell":
            logger_print(msg=f"double click detected on treeview row: {cur_tree.item(cur_tree.focus())}", custom_logger=logger, log_level=logging.DEBUG)
            funct()
    cur_tree.bind("<Double-1>",func=double_click_handler)

def gui_exist(cur_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]])->bool:
    """判断窗口是否存在"""
    try:
        return cur_gui is not None and cur_gui.winfo_exists()
    except tkinter.TclError:
      return False

def gui_close(cur_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]]):
    """关闭窗口: 判断对应窗口是否存在,存在时关闭窗口"""
    if not  gui_exist(cur_gui):
        return
    try:
        if isinstance(cur_gui, tkinter.Tk):
            cur_gui.quit()
        cur_gui.destroy()
    except tkinter.TclError:
        # 窗口可能在这两步中已经被系统销毁了
        pass

def bring_to_front(win: tkinter.Tk):
    """将主界面窗口置顶"""
    win.lift()
    # 先将窗口置顶
    win.attributes('-topmost', True)
    # 空闲时再取消置顶
    win.after(100, lambda: win.attributes('-topmost', False)) # noqa
    # 强制获取焦点
    win.focus_force()

def rgb_to_hex(rgb):
    """
    将RGB元组转换为16进制颜色字符串:
    :param rgb: RGB元组
    :return: 16进制颜色字符串
    示例: rgb_to_hex((255, 0, 0))  # '#ff0000'
    """
    return '#{:02x}{:02x}{:02x}'.format(int(rgb[0]), int(rgb[1]), int(rgb[2]))

def create_color_circle_image(color,row_height=25):
    """
    创建一个与行高匹配的颜色圆形图像
    :param row_height: 行高
    :param color: 颜色字符串或RGB元组
    :return: ImageTk.PhotoImage 实例
    """
    final_size = row_height - 4         # 最终尺寸
    if final_size < 16:  # 最小尺寸限制
        final_size = 16
    scale_factor = 4        # 缩放因子：越大越平滑
    size = final_size * scale_factor  # 临时画布尺寸

    # 创建透明背景图像
    image = Image.new("RGBA", (size, size), (255, 255, 255, 0))
    draw = ImageDraw.Draw(image)
    # 绘制大尺寸的圆
    draw.ellipse((0, 0, size - 1, size - 1), fill=color, outline=color)
    # 缩小图像以达到抗锯齿效果
    image = image.resize((final_size, final_size), Image.LANCZOS)
    return ImageTk.PhotoImage(image)

def create_red_x_image(row_height=25):
    """创建一个与行高匹配的红色'X'图像"""
    # 计算图像尺寸（行高减去4像素的边距）
    size = row_height - 4
    if size < 16:  # 最小尺寸限制
        size = 16

    # 创建透明背景图像
    image = Image.new("RGBA", (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)

    # 绘制红色"X"
    draw.line([(2, 2), (size-2, size-2)], fill="red", width=2)
    draw.line([(size-2, 2), (2, size-2)], fill="red", width=2)

    # 应用平滑滤镜
    image = image.filter(ImageFilter.SMOOTH)
    return ImageTk.PhotoImage(image)

def main_gui_after_create_widgets_comm_do(win: tkinter.Tk):
    """在主界面创建完控件后执行的通用操作:1. 居中主界面; 2. 前置主窗口"""
    center_window_on_screen(win)
    bring_to_front(win)


def is_mouse_inside_widget(widget) -> bool:
    """
    判断鼠标是否在 widget 控件的区域内（无扩展边距）

    :param widget: 任意 Tkinter 控件
    :return: bool，鼠标是否在控件内部
    """
    x1 = widget.winfo_rootx()
    y1 = widget.winfo_rooty()
    x2 = x1 + widget.winfo_width()
    y2 = y1 + widget.winfo_height()

    # 获取鼠标在屏幕上的实时坐标
    mouse_x = widget.winfo_pointerx()
    mouse_y = widget.winfo_pointery()

    return x1 <= mouse_x <= x2 and y1 <= mouse_y <= y2
